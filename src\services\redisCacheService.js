import Redis from 'ioredis';
import logger from '../utils/logger.js';

/**
 * Redis Cache Service for Trading Signals App
 *
 * Provides high-performance caching with specific strategies for different data types:
 * - Market prices: 30-second TTL with real-time invalidation
 * - Technical indicators: 5-minute TTL with symbol-based keys
 * - AI analysis results: 10-minute TTL with model+symbol+timeframe composite keys
 * - User sessions: 24-hour TTL with automatic refresh
 *
 * @class RedisCacheService
 * @version 1.0.0
 */
export class RedisCacheService {
  /**
   * Initialize Redis cache service
   *
   * @constructor
   */
  constructor() {
    this.redis = null;
    this.isConnected = false;
    this.connectionRetries = 0;
    this.maxRetries = 5;

    // Cache TTL configurations (in seconds)
    this.ttlConfig = {
      MARKET_PRICES: 30,           // 30 seconds
      TECHNICAL_INDICATORS: 300,   // 5 minutes
      AI_ANALYSIS: 600,           // 10 minutes
      USER_SESSIONS: 86400,       // 24 hours
      NEWS_SENTIMENT: 1800,       // 30 minutes
      ECONOMIC_CALENDAR: 3600,    // 1 hour
      DEFAULT: 300                // 5 minutes
    };

    // Cache key prefixes
    this.keyPrefixes = {
      MARKET_PRICES: 'mp',
      TECHNICAL_INDICATORS: 'ti',
      AI_ANALYSIS: 'ai',
      USER_SESSIONS: 'us',
      NEWS_SENTIMENT: 'ns',
      ECONOMIC_CALENDAR: 'ec'
    };

    // Performance metrics
    this.metrics = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      errors: 0,
      totalRequests: 0
    };

    this.initialize();
  }

  /**
   * Initialize Redis connection
   *
   * @private
   */
  async initialize() {
    try {
      const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';

      this.redis = new Redis(redisUrl, {
        retryDelayOnFailover: 100,
        enableReadyCheck: true,
        maxRetriesPerRequest: 3,
        lazyConnect: true,
        keepAlive: 30000,
        connectTimeout: 10000,
        commandTimeout: 5000,
        db: parseInt(process.env.REDIS_DB || '0', 10)
      });

      // Event handlers
      this.redis.on('connect', () => {
        logger.info('Redis cache service connected');
        this.isConnected = true;
        this.connectionRetries = 0;
      });

      this.redis.on('error', (error) => {
        logger.error('Redis cache service error:', error);
        this.isConnected = false;
        this.metrics.errors++;
      });

      this.redis.on('close', () => {
        logger.warn('Redis cache service connection closed');
        this.isConnected = false;
      });

      this.redis.on('reconnecting', () => {
        this.connectionRetries++;
        logger.info(`Redis cache service reconnecting (attempt ${this.connectionRetries})`);
      });

      // Connect to Redis
      await this.redis.connect();

      // Test connection
      await this.redis.ping();
      logger.info('Redis cache service initialized successfully');

    } catch (error) {
      logger.error('Failed to initialize Redis cache service:', error);
      this.isConnected = false;
    }
  }

  /**
   * Generate cache key with prefix and parameters
   *
   * @private
   * @param {string} prefix - Cache key prefix
   * @param {Array} params - Parameters to include in key
   * @returns {string} Generated cache key
   */
  generateKey(prefix, ...params) {
    const cleanParams = params
      .filter(param => param !== null && param !== undefined)
      .map(param => String(param).toLowerCase().replace(/[^a-z0-9]/g, '_'));

    return `${prefix}:${cleanParams.join(':')}`;
  }

  /**
   * Cache market prices with 30-second TTL
   *
   * @param {string} symbol - Trading symbol
   * @param {string} timeframe - Chart timeframe
   * @param {Object} data - Market price data
   * @returns {Promise<boolean>} Success status
   */
  async cacheMarketPrices(symbol, timeframe, data) {
    if (!this.isConnected) return false;

    try {
      const key = this.generateKey(this.keyPrefixes.MARKET_PRICES, symbol, timeframe);
      const serializedData = JSON.stringify({
        data,
        timestamp: Date.now(),
        symbol: symbol.toUpperCase(),
        timeframe
      });

      await this.redis.setex(key, this.ttlConfig.MARKET_PRICES, serializedData);
      this.metrics.sets++;

      logger.debug(`Cached market prices for ${symbol}:${timeframe}`);
      return true;
    } catch (error) {
      logger.error('Error caching market prices:', error);
      this.metrics.errors++;
      return false;
    }
  }

  /**
   * Get cached market prices
   *
   * @param {string} symbol - Trading symbol
   * @param {string} timeframe - Chart timeframe
   * @returns {Promise<Object|null>} Cached data or null
   */
  async getMarketPrices(symbol, timeframe) {
    if (!this.isConnected) return null;

    try {
      const key = this.generateKey(this.keyPrefixes.MARKET_PRICES, symbol, timeframe);
      const cached = await this.redis.get(key);

      this.metrics.totalRequests++;

      if (cached) {
        this.metrics.hits++;
        const parsed = JSON.parse(cached);
        logger.debug(`Cache hit for market prices ${symbol}:${timeframe}`);
        return parsed;
      }

      this.metrics.misses++;
      return null;
    } catch (error) {
      logger.error('Error getting cached market prices:', error);
      this.metrics.errors++;
      return null;
    }
  }

  /**
   * Cache technical indicators with 5-minute TTL
   *
   * @param {string} symbol - Trading symbol
   * @param {string} timeframe - Chart timeframe
   * @param {Object} indicators - Technical indicators data
   * @returns {Promise<boolean>} Success status
   */
  async cacheTechnicalIndicators(symbol, timeframe, indicators) {
    if (!this.isConnected) return false;

    try {
      const key = this.generateKey(this.keyPrefixes.TECHNICAL_INDICATORS, symbol, timeframe);
      const serializedData = JSON.stringify({
        indicators,
        timestamp: Date.now(),
        symbol: symbol.toUpperCase(),
        timeframe
      });

      await this.redis.setex(key, this.ttlConfig.TECHNICAL_INDICATORS, serializedData);
      this.metrics.sets++;

      logger.debug(`Cached technical indicators for ${symbol}:${timeframe}`);
      return true;
    } catch (error) {
      logger.error('Error caching technical indicators:', error);
      this.metrics.errors++;
      return false;
    }
  }

  /**
   * Get cached technical indicators
   *
   * @param {string} symbol - Trading symbol
   * @param {string} timeframe - Chart timeframe
   * @returns {Promise<Object|null>} Cached data or null
   */
  async getTechnicalIndicators(symbol, timeframe) {
    if (!this.isConnected) return null;

    try {
      const key = this.generateKey(this.keyPrefixes.TECHNICAL_INDICATORS, symbol, timeframe);
      const cached = await this.redis.get(key);

      this.metrics.totalRequests++;

      if (cached) {
        this.metrics.hits++;
        const parsed = JSON.parse(cached);
        logger.debug(`Cache hit for technical indicators ${symbol}:${timeframe}`);
        return parsed;
      }

      this.metrics.misses++;
      return null;
    } catch (error) {
      logger.error('Error getting cached technical indicators:', error);
      this.metrics.errors++;
      return null;
    }
  }

  /**
   * Cache AI analysis results with 10-minute TTL
   *
   * @param {string} symbol - Trading symbol
   * @param {string} timeframe - Chart timeframe
   * @param {string} model - AI model used
   * @param {string} analysisType - Type of analysis
   * @param {Object} result - Analysis result
   * @returns {Promise<boolean>} Success status
   */
  async cacheAIAnalysis(symbol, timeframe, model, analysisType, result) {
    if (!this.isConnected) return false;

    try {
      const key = this.generateKey(this.keyPrefixes.AI_ANALYSIS, symbol, timeframe, model, analysisType);
      const serializedData = JSON.stringify({
        result,
        timestamp: Date.now(),
        symbol: symbol.toUpperCase(),
        timeframe,
        model,
        analysisType
      });

      await this.redis.setex(key, this.ttlConfig.AI_ANALYSIS, serializedData);
      this.metrics.sets++;

      logger.debug(`Cached AI analysis for ${symbol}:${timeframe}:${model}:${analysisType}`);
      return true;
    } catch (error) {
      logger.error('Error caching AI analysis:', error);
      this.metrics.errors++;
      return false;
    }
  }

  /**
   * Get cached AI analysis results
   *
   * @param {string} symbol - Trading symbol
   * @param {string} timeframe - Chart timeframe
   * @param {string} model - AI model used
   * @param {string} analysisType - Type of analysis
   * @returns {Promise<Object|null>} Cached data or null
   */
  async getAIAnalysis(symbol, timeframe, model, analysisType) {
    if (!this.isConnected) return null;

    try {
      const key = this.generateKey(this.keyPrefixes.AI_ANALYSIS, symbol, timeframe, model, analysisType);
      const cached = await this.redis.get(key);

      this.metrics.totalRequests++;

      if (cached) {
        this.metrics.hits++;
        const parsed = JSON.parse(cached);
        logger.debug(`Cache hit for AI analysis ${symbol}:${timeframe}:${model}:${analysisType}`);
        return parsed;
      }

      this.metrics.misses++;
      return null;
    } catch (error) {
      logger.error('Error getting cached AI analysis:', error);
      this.metrics.errors++;
      return null;
    }
  }

  /**
   * Cache user session with 24-hour TTL
   *
   * @param {string} sessionId - Session identifier
   * @param {Object} sessionData - Session data
   * @returns {Promise<boolean>} Success status
   */
  async cacheUserSession(sessionId, sessionData) {
    if (!this.isConnected) return false;

    try {
      const key = this.generateKey(this.keyPrefixes.USER_SESSIONS, sessionId);
      const serializedData = JSON.stringify({
        ...sessionData,
        timestamp: Date.now(),
        lastAccessed: Date.now()
      });

      await this.redis.setex(key, this.ttlConfig.USER_SESSIONS, serializedData);
      this.metrics.sets++;

      logger.debug(`Cached user session ${sessionId}`);
      return true;
    } catch (error) {
      logger.error('Error caching user session:', error);
      this.metrics.errors++;
      return false;
    }
  }

  /**
   * Get cached user session
   *
   * @param {string} sessionId - Session identifier
   * @returns {Promise<Object|null>} Cached session data or null
   */
  async getUserSession(sessionId) {
    if (!this.isConnected) return null;

    try {
      const key = this.generateKey(this.keyPrefixes.USER_SESSIONS, sessionId);
      const cached = await this.redis.get(key);

      this.metrics.totalRequests++;

      if (cached) {
        this.metrics.hits++;
        const parsed = JSON.parse(cached);

        // Update last accessed time
        parsed.lastAccessed = Date.now();
        await this.redis.setex(key, this.ttlConfig.USER_SESSIONS, JSON.stringify(parsed));

        logger.debug(`Cache hit for user session ${sessionId}`);
        return parsed;
      }

      this.metrics.misses++;
      return null;
    } catch (error) {
      logger.error('Error getting cached user session:', error);
      this.metrics.errors++;
      return null;
    }
  }

  /**
   * Invalidate cache entries by pattern
   *
   * @param {string} pattern - Redis key pattern
   * @returns {Promise<number>} Number of keys deleted
   */
  async invalidateByPattern(pattern) {
    if (!this.isConnected) return 0;

    try {
      const keys = await this.redis.keys(pattern);
      if (keys.length === 0) return 0;

      const deleted = await this.redis.del(...keys);
      this.metrics.deletes += deleted;

      logger.info(`Invalidated ${deleted} cache entries matching pattern: ${pattern}`);
      return deleted;
    } catch (error) {
      logger.error('Error invalidating cache by pattern:', error);
      this.metrics.errors++;
      return 0;
    }
  }

  /**
   * Invalidate market data cache for symbol
   *
   * @param {string} symbol - Trading symbol
   * @returns {Promise<number>} Number of keys deleted
   */
  async invalidateMarketData(symbol) {
    const pattern = `${this.keyPrefixes.MARKET_PRICES}:${symbol.toLowerCase()}:*`;
    return await this.invalidateByPattern(pattern);
  }

  /**
   * Invalidate technical indicators cache for symbol
   *
   * @param {string} symbol - Trading symbol
   * @returns {Promise<number>} Number of keys deleted
   */
  async invalidateTechnicalIndicators(symbol) {
    const pattern = `${this.keyPrefixes.TECHNICAL_INDICATORS}:${symbol.toLowerCase()}:*`;
    return await this.invalidateByPattern(pattern);
  }

  /**
   * Get cache performance metrics
   *
   * @returns {Object} Performance metrics
   */
  getMetrics() {
    const hitRate = this.metrics.totalRequests > 0 ?
      (this.metrics.hits / this.metrics.totalRequests * 100).toFixed(2) : 0;

    return {
      ...this.metrics,
      hitRate: `${hitRate}%`,
      isConnected: this.isConnected,
      connectionRetries: this.connectionRetries
    };
  }

  /**
   * Reset performance metrics
   */
  resetMetrics() {
    this.metrics = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      errors: 0,
      totalRequests: 0
    };
    logger.info('Cache metrics reset');
  }

  /**
   * Get cache info and statistics
   *
   * @returns {Promise<Object>} Cache information
   */
  async getCacheInfo() {
    if (!this.isConnected) {
      return { connected: false, error: 'Redis not connected' };
    }

    try {
      const info = await this.redis.info('memory');
      const dbSize = await this.redis.dbsize();

      return {
        connected: true,
        dbSize,
        memoryInfo: this.parseRedisInfo(info),
        metrics: this.getMetrics(),
        ttlConfig: this.ttlConfig
      };
    } catch (error) {
      logger.error('Error getting cache info:', error);
      return { connected: false, error: error.message };
    }
  }

  /**
   * Parse Redis INFO command output
   *
   * @private
   * @param {string} info - Redis INFO output
   * @returns {Object} Parsed memory information
   */
  parseRedisInfo(info) {
    const lines = info.split('\r\n');
    const memoryInfo = {};

    lines.forEach(line => {
      if (line.includes(':')) {
        const [key, value] = line.split(':');
        if (key.includes('memory') || key.includes('used')) {
          memoryInfo[key] = value;
        }
      }
    });

    return memoryInfo;
  }

  /**
   * Flush all cache data
   *
   * @returns {Promise<boolean>} Success status
   */
  async flushAll() {
    if (!this.isConnected) return false;

    try {
      await this.redis.flushdb();
      this.resetMetrics();
      logger.warn('All cache data flushed');
      return true;
    } catch (error) {
      logger.error('Error flushing cache:', error);
      return false;
    }
  }

  /**
   * Close Redis connection
   */
  async close() {
    if (this.redis) {
      await this.redis.quit();
      this.isConnected = false;
      logger.info('Redis cache service connection closed');
    }
  }
}

// Create and export singleton instance
export const redisCacheService = new RedisCacheService();
export default redisCacheService;
